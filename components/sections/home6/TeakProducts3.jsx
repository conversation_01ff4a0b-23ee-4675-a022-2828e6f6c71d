"use client";
import React from "react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";
import { useTranslations } from "next-intl";

import Parquet from "@/components/layout/products/parquet";
import Flooring from "@/components/layout/products/flooring";
import Decking from "@/components/layout/products/decking";
import Cladding from "@/components/layout/products/cladding";
import Timber from "@/components/layout/products/timber";
import Fingerjoint from "@/components/layout/products/finger-jointed";
import Skirting from "@/components/layout/products/skirting";

const swiperOptions = {
  modules: [Autoplay, Pagination, Navigation],
  slidesPerView: 1,
  spaceBetween: 30,
  autoHeight: true,
  // Autoplay
  // autoplay: {
  //   delay: 5000,
  //   disableOnInteraction: false,
  //   speed: 800,
  // },
  loop: true,
  // simulateTouch: false,

  // Navigation
  // navigation: {
  //   nextEl: ".swiper-arrow-next",
  //   prevEl: ".swiper-arrow-prev",
  // },

  // Pagination
  pagination: {
    el: ".swiper-pagination",
    clickable: true,
  },

  breakpoints: {
    // when window width is >= 320px
    320: {
      simulateTouch: false,
    },
    // when window width is >= 480px
    480: {
      simulateTouch: false,
    },
    // when window width is >= 640px
    640: {
      simulateTouch: false,
    },
    // when window width is >= 768px
    768: {
      simulateTouch: true,
    },
  },
};

export default function TeakProducts3() {
  const t = useTranslations("thaiTeak");

  return (
    <>
      {/*Testimonial Two Start*/}
      <section className="product-container">
        <div
          className="section-shape-1"
          style={{
            backgroundImage: " url(/assets/images/shapes/section-shape-1.png)",
          }}
        ></div>
        <div className="container">
          <div className="product-title text-center sec-title-animation animation-style1">
            <h2 className="section-title__title title-animation">
              {t("Products.title")}
            </h2>
          </div>
          <div className="testimonial-two__bottom">
            <Swiper
              {...swiperOptions}
              className="testimonial-two__carousel owl-carousel owl-theme thm-owl__carousel"
            >
              <SwiperSlide>
                <Parquet />
              </SwiperSlide>
              <SwiperSlide>
                <Flooring />
              </SwiperSlide>
              <SwiperSlide>
                <Decking />
              </SwiperSlide>
              <SwiperSlide>
                <Cladding />
              </SwiperSlide>
              <SwiperSlide>
                <Timber />
              </SwiperSlide>
              <SwiperSlide>
                <Fingerjoint />
              </SwiperSlide>
              <SwiperSlide>
                <Skirting />
              </SwiperSlide>
            </Swiper>
            <div className="swiper-pagination"></div>
          </div>
        </div>
        <div className="btn-box d-flex justify-content-center">
          <a
            className="dwn-btn"
            href="/assets/images/pricelist/sakw-teak-pricelist.pdf"
            download
            target="_blank"
            rel="noopener noreferrer"
          >
            {t("download.buttonText")}{" "}
            <span className="fas fa-file-download"></span>
          </a>
        </div>
        {/* <div className="main-slider-nav2">
          <div className="swiper-arrow-prev">
            <span className="icon-left-arrow"></span>
          </div>
          <div className="swiper-arrow-next">
            <span className="icon-right-arrow"></span>
          </div>
        </div> */}
      </section>
      {/*Testimonial Two End*/}
    </>
  );
}
