"use client";
import React from "react";
import { useTranslations } from "next-intl";

import Parquet from "@/components/layout/products/parquet";
import Flooring from "@/components/layout/products/flooring";
import Decking from "@/components/layout/products/decking";
import Cladding from "@/components/layout/products/cladding";
import Timber from "@/components/layout/products/timber";
import Fingerjoint from "@/components/layout/products/finger-jointed";
import Skirting from "@/components/layout/products/skirting";

export default function TeakProducts3() {
  const t = useTranslations("thaiTeak");

  return (
    <>
      {/*Testimonial Two Start*/}
      <section className="product-container">
        <div
          className="section-shape-1"
          style={{
            backgroundImage: " url(/assets/images/shapes/section-shape-1.png)",
          }}
        ></div>
        <div className="container">
          <div className="product-title text-center sec-title-animation animation-style1">
            <h2 className="section-title__title title-animation">
              {t("Products.title")}
            </h2>
          </div>
          <div className="testimonial-two__bottom">
            <Parquet />
            <Flooring />
            <Decking />
            <Cladding />
            <Timber />
            <Fingerjoint />
            <Skirting />
          </div>
        </div>
        <div className="btn-box d-flex justify-content-center">
          <a
            className="dwn-btn"
            href="/assets/images/pricelist/sakw-teak-pricelist.pdf"
            download
            target="_blank"
            rel="noopener noreferrer"
          >
            {t("download.buttonText")}{" "}
            <span className="fas fa-file-download"></span>
          </a>
        </div>
      </section>
      {/*Testimonial Two End*/}
    </>
  );
}
