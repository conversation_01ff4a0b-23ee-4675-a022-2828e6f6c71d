"use client";

import { useTranslations } from "next-intl";
export default function fingerjoint() {
  const t = useTranslations("thaiTeak");
  const tb = useTranslations("table");

  return (
    <>
      <div className="product__single">
        <div className="testimonial-two__image">
          <img src="/assets/images/products/teak/Finger-Joint.png" alt="" />
        </div>
        <div className="row">
          <div className="product-title-1">
            <span className="col-xl-6 col-lg-6">
              <h3>{t("Products.list6.title")}</h3>{" "}
              <h5>{t("Products.list6.subtitle")}</h5>
              {/* <h3>TEAK FINGER-JOINT PANELS</h3>{" "}
                <h5>Excellent for Variety Applications.</h5> */}
            </span>
            <img
              className="product-thumbnail col-xl-6 col-lg-6"
              src="/assets/images/products/teak/fingerjoints_thumb.svg"
              alt="Product thumbnail"
            />
          </div>
        </div>
        <div className="product-details__img-and-faq">
          <div className="row">
            <div className="col-xl-6 col-lg-5">
              <div className="projects-one__single">
                <div className="projects-one__img-box">
                  <div className="product-hilight__img">
                    <img
                      src="/assets/images/products/highlight/teak-fingerjoint.png"
                      alt=""
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="col-xl-6 col-lg-7">
              {/* <div className="project-details__faq-box">
                  <p className="product-details__text-1">
                    Our teak parquet is the foundation of a luxurious interior.
                    Its added value lies in its substantial thickness, a mark of
                    heirloom quality proven by time.
                  </p>
                  <p className="product-details__text-2">
                    Greater thickness gives the floor an outstanding refinishing
                    lifespan, allowing the surface to be restored numerous
                    times. This ensures your real wood floors can always be
                    returned to their original splendor and passed down through
                    generations. Furthermore, its four-sided interlocking system
                    offers the creative freedom to design complex and unique
                    installation patterns.
                  </p>
                  <h4 className="service-details__points-title">
                    Technical Specifications
                  </h4>
                  <p className="service-details__text-3">
                    Species: Thai Teak (Tectona grandis)
                    <br />
                    Origin: Plantation Forests in the Northern Thailand
                    <br />
                    Wood Grade: Natural Select Grade <br />
                    Drying: Kiln Dried (KD) <br />
                    Moisture Content: 12-14% <br />
                  </p>
                </div> */}
              {/* Price table */}
              <div className="product-table-container">
                <table className="table table-borderless product-table">
                  <thead>
                    <tr>
                      <th scope="col">{tb("col1")}</th>
                      <th scope="col">{tb("col2")}</th>
                      <th scope="col">{tb("col3")}</th>
                      <th scope="col">{tb("col4")}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>
                        <div className="product-info">16 x 200 mm</div>
                      </td>
                      <td>1200 mm</td>
                      <td>
                        <div className="price-text">5 900 ₽</div>
                      </td>
                      <td>
                        <div className="unit">PCS</div>
                      </td>
                    </tr>

                    <tr className="divider-row">
                      <td colSpan="4"></td>
                    </tr>

                    <tr>
                      <td>
                        <div className="product-info">22 x 300 mm</div>
                      </td>
                      <td>1200 mm</td>
                      <td>
                        <div className="price-text">9 900 ₽</div>
                      </td>
                      <td>
                        <div className="unit">PCS</div>
                      </td>
                    </tr>

                    <tr className="divider-row">
                      <td colSpan="4"></td>
                    </tr>

                    <tr>
                      <td>
                        <div className="product-info">35 x 300 mm</div>
                      </td>
                      <td>1200 mm</td>
                      <td>
                        <div className="price-text">14 900 ₽</div>
                      </td>
                      <td>
                        <div className="unit">PCS</div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
