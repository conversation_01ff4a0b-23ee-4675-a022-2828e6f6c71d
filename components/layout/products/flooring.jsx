"use client";

import { useTranslations } from "next-intl";
export default function flooring() {
  const t = useTranslations("thaiTeak");
  const tb = useTranslations("table");

  return (
    <>
      <div className="product__single">
        <div className="testimonial-two__image">
          <img src="/assets/images/products/teak/Floorboard.png" alt="" />
        </div>
        <div className="row">
          <div className="product-title-1">
            <span className="col-xl-6 col-lg-6">
              <h3>{t("Products.list2.title")}</h3>{" "}
              <h5>{t("Products.list2.subtitle")}</h5>
            </span>
            <img
              className="product-thumbnail col-xl-6 col-lg-6"
              src="/assets/images/products/teak/flooring_thumb.svg"
              alt="Product thumbnail"
            />
          </div>
        </div>
        <div className="product-details__img-and-faq">
          <div className="row">
            <div className="col-xl-6 col-lg-5">
              <div className="projects-one__single">
                <div className="projects-one__img-box">
                  <div className="product-hilight__img">
                    <img
                      src="/assets/images/products/highlight/teak-flooring.png"
                      alt=""
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="col-xl-6 col-lg-7">
              <div className="product-table-container">
                <table className="table table-borderless product-table">
                  <thead>
                    <tr>
                      <th scope="col">{tb("col1")}</th>
                      <th scope="col">{tb("col2")}</th>
                      <th scope="col">{tb("col3")}</th>
                      <th scope="col">{tb("col4")}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td rowSpan="3">
                        <div className="product-info">22 x 115 mm</div>
                      </td>
                      <td>900, 1200 mm</td>
                      <td>
                        <div className="price-text">18 000 ₽</div>
                      </td>
                      <td>
                        <div className="unit">SQM</div>
                      </td>
                    </tr>
                    <tr className="product-highlight">
                      <td>1500, 1800 mm</td>
                      <td>
                        <div className="price-text">20 000 ₽</div>
                      </td>
                      <td>
                        <div className="unit">SQM</div>
                      </td>
                    </tr>
                    <tr>
                      <td>2100, 2400 mm</td>
                      <td>
                        <div className="price-text">22 000 ₽</div>
                      </td>
                      <td>
                        <div className="unit">SQM</div>
                      </td>
                    </tr>

                    <tr className="divider-row">
                      <td colSpan="4"></td>
                    </tr>

                    <tr>
                      <td rowSpan="3">
                        <div className="product-info">22 x 140 mm</div>
                      </td>
                      <td>900, 1200 mm</td>
                      <td>
                        <div className="price-text">19 800 ₽</div>
                      </td>
                      <td>
                        <div className="unit">SQM</div>
                      </td>
                    </tr>
                    <tr className="product-highlight">
                      <td>1500, 1800 mm</td>
                      <td>
                        <div className="price-text">21 800 ₽</div>
                      </td>
                      <td>
                        <div className="unit">SQM</div>
                      </td>
                    </tr>
                    <tr>
                      <td>2100, 2400 mm</td>
                      <td>
                        <div className="price-text">23 800 ₽</div>
                      </td>
                      <td>
                        <div className="unit">SQM</div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
