"use client";

import { useTranslations } from "next-intl";
export default function decking() {
  const t = useTranslations("thaiTeak");
  const tb = useTranslations("table");

  return (
    <>
      <div className="product__single">
        <div className="testimonial-two__image">
          <img src="/assets/images/products/teak/Decking.png" alt="" />
        </div>
        <div className="row">
          <div className="product-title-1">
            <span className="col-xl-6 col-lg-6">
              <h3>{t("Products.list3.title")}</h3>{" "}
              <h5>{t("Products.list3.subtitle")}</h5>
            </span>
            <img
              className="product-thumbnail col-xl-6 col-lg-6"
              src="/assets/images/products/teak/decking_thumb.svg"
              alt="Product thumbnail"
            />
          </div>
        </div>
        <div className="product-details__img-and-faq">
          <div className="row">
            <div className="col-xl-6 col-lg-5">
              <div className="projects-one__single">
                <div className="projects-one__img-box">
                  <div className="product-hilight__img">
                    <img
                      src="/assets/images/products/highlight/teak-decking.png"
                      alt=""
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="col-xl-6 col-lg-7">
              {/* Price table */}
              <div className="product-table-container">
                <table className="table table-borderless product-table">
                  <thead>
                    <tr>
                      <th scope="col">{tb("col1")}</th>
                      <th scope="col">{tb("col2")}</th>
                      <th scope="col">{tb("col3")}</th>
                      <th scope="col">{tb("col4")}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td rowSpan="2">
                        <div className="product-info">15 x 90 mm</div>
                      </td>
                      <td>1200, 1500 mm</td>
                      <td>
                        <div className="price-text">18 000 ₽</div>
                      </td>
                      <td>
                        <div className="unit">SQM</div>
                      </td>
                    </tr>
                    <tr className="product-highlight">
                      <td>1800, 2100, 2400 mm</td>
                      <td>
                        <div className="price-text">21 000 ₽</div>
                      </td>
                      <td>
                        <div className="unit">SQM</div>
                      </td>
                    </tr>

                    <tr className="divider-row">
                      <td colSpan="4"></td>
                    </tr>

                    <tr>
                      <td rowSpan="2">
                        <div className="product-info">15 x 140 mm</div>
                      </td>
                      <td>1200, 1500 mm</td>
                      <td>
                        <div className="price-text">20 000 ₽</div>
                      </td>
                      <td>
                        <div className="unit">SQM</div>
                      </td>
                    </tr>
                    <tr className="product-highlight">
                      <td>1800, 2100, 2400 mm</td>
                      <td>
                        <div className="price-text">22 000 ₽</div>
                      </td>
                      <td>
                        <div className="unit">SQM</div>
                      </td>
                    </tr>

                    <tr className="divider-row">
                      <td colSpan="4"></td>
                    </tr>

                    <tr>
                      <td rowSpan="2">
                        <div className="product-info">22 x 90 mm</div>
                      </td>
                      <td>1200, 1500 mm</td>
                      <td>
                        <div className="price-text">24 000 ₽</div>
                      </td>
                      <td>
                        <div className="unit">SQM</div>
                      </td>
                    </tr>
                    <tr className="product-highlight">
                      <td>1800, 2100, 2400 mm</td>
                      <td>
                        <div className="price-text">26 000 ₽</div>
                      </td>
                      <td>
                        <div className="unit">SQM</div>
                      </td>
                    </tr>

                    <tr className="divider-row">
                      <td colSpan="4"></td>
                    </tr>

                    <tr>
                      <td rowSpan="2">
                        <div className="product-info">22 x 140 mm</div>
                      </td>
                      <td>1200, 1500, 1800, 2100, 2400 mm</td>
                      <td>
                        <div className="price-text">26 000 ₽</div>
                      </td>
                      <td>
                        <div className="unit">SQM</div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
