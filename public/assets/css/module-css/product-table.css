.product-container {
  border-top: 1px solid var(--tecture-bdr-color);
  z-index: 1;
  padding: 120px 0;
  display: block;
  position: relative;
  overflow: hidden;
}

.product__single {
  position: relative;
  display: block;
  background-color: rgba(63, 32, 33, 0.6);
  border-radius: 20px;
  z-index: 1;
  margin-bottom: 30px;
  text-align: left;
  padding: 80px 45px 45px;
}

.hardwoods__single {
  position: relative;
  display: block;
  background-color: rgba(63, 32, 33, 0.6);
  border-radius: 20px;
  z-index: 1;
  margin-bottom: 30px;
  text-align: left;
  padding: 45px 45px;
}

.product-title {
  margin-bottom: 30px;
}

.product-title-1 {
  display: flex;
  align-items: end;
  gap: 15px;
}

.product-thumbnail {
  width: 180px;
  height: auto;
}

.product-table-container {
  background-color: var(--tecture-black);
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch; /* For smooth scrolling on iOS devices */
}

.product-table {
  width: 100%;
  /* margin-top: 30px; */
  border-collapse: separate;
  border-spacing: 0; /* Adds space between rows */
  padding: 0 20px;
  min-height: 505px;
  min-width: max-content; /* Ensures the table doesn't collapse on small screens */
  z-index: 1;
}

.product-table th,
.product-table td {
  padding: 8px 8px;
  vertical-align: middle;
  color: #d6d2c4;
}

.product-table thead th {
  color: #d1ae8b; /* Lighter color for header text */
  font-weight: 500;
  border-bottom: 0.5px solid rgba(255, 255, 255, 0.6); /* Separator line for the header */
}

.product-table thead th:last-child {
  text-align: center;
}

.product-table tbody tr {
  border-bottom: 1px solid rgba(255, 255, 255, 0.6); /* Separator line for rows */
}

.product-table tbody tr:last-child {
  border-bottom: none; /* No border for the last row */
}

.product-table tbody td {
  font-size: 13px;
  font-weight: 300;
}

.product-table tbody td:last-child {
  text-align: center;
}

.product-details__text-1 {
  min-height: none;
}

.product-details__text-2 {
  margin-top: 20px;
  margin-bottom: 36px;
}

.product-details__img-and-faq {
  margin-top: 20px;
  /* margin-bottom: 43px; */
  display: block;
  position: relative;
}

.product-list2 {
  background-color: #3d3d3d;
}

.product-highlight {
  background-color: rgba(217, 217, 217, 0.2);
}

.product-hilight__img {
  position: relative;
  display: block;
  overflow: hidden;
  z-index: 1;
  max-height: 520px;
}

.product-hilight__img img {
  width: 100%;
  height: auto;
}

.price-text {
  color: #d1ae8b;
}

/* Custom styling for the divider rows to create the gaps */
.divider-row td {
  border-bottom: 0.5px solid rgba(255, 255, 255, 0.6); /* The horizontal line */
  position: relative;
  padding: 0px;
}

/* Specific cell styling */
.product-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #d6d2c4;
  font-weight: 500;
}

.product-info img {
  border: 1px solid #c9cdd0; /* Border around the image */
  border-radius: 2px;
}

.product-container .swiper-pagination {
  position: relative;
  bottom: 0;
  /* padding: 20px 0; */
}

.product-container .swiper-pagination .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  background-color: rgba(
    255,
    255,
    255,
    0.2
  ); /* Edit this for the inactive bullet color */
  opacity: 1;
  border-radius: 50%;
  transition: all 500ms ease;
  margin: 0 5px;
}

.product-container .swiper-pagination .swiper-pagination-bullet:hover,
.product-container
  .swiper-pagination
  .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: #d1ae8b; /* Edit this for the active bullet color */
  transform: scale(1.2);
}

.btn-box {
  margin-top: 15px;
}

.hardwoods__content-col {
  display: flex;
  flex-direction: column;
}

.hardwoods-title {
  display: flex;
  align-items: end;
  gap: 15px;
}

.hardwoods__btn {
  margin-top: auto;
  padding-top: 20px; /* Optional: adds some space above the button */
}

/* .unit {
  padding-left: 22px;
} */
