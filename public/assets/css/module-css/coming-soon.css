/*--------------------------------------------------------------
# Coming Soon Page
--------------------------------------------------------------*/
.coming-soon-page {
    position: relative;
    display: block;
    overflow: hidden;
    z-index: 10;
}

.coming-soon-page__bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-attachment: scroll;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    background-color: var(--tecture-black);
    background-blend-mode: luminosity;
    z-index: -1;
}

.coming-soon-page__bg:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(0deg, #000000 5%, rgba(0, 0, 0, 0.5) 100%);
}

.coming-soon-page__content {
    position: relative;
    display: block;
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding: 0 15px;
    top: 50%;
    transform: translateY(-50%);
}

.coming-soon-page__content .inner {
    position: relative;
    display: block;
    width: 100%;
    height: 100%;
    text-align: center;
}

.coming-soon-page__content .big-title {
    position: relative;
    display: inline-block;
    color: transparent;
    font-size: 80px;
    line-height: 1.1em;
    font-family: var(--tecture-font-2);
    font-weight: 700;
    text-transform: uppercase;
    -webkit-background-clip: text;
    -webkit-text-stroke: 1px var(--tecture-white);
    word-spacing: 15px;
}

.coming-soon-page .timer-box {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 57px 0 42px;
    z-index: 2;
}

.coming-soon-page .timer-box .countdown-timer li {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    float: left;
    width: 170px;
    height: 170px;
    margin: 0px 15px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    background-color: transparent;
    border-radius: 50%;
}

.coming-soon-page .timer-box .countdown-timer li span.days,
.coming-soon-page .timer-box .countdown-timer li span.hours,
.coming-soon-page .timer-box .countdown-timer li span.minutes,
.coming-soon-page .timer-box .countdown-timer li span.seconds {
    color: #ffffff;
    font-size: 55px;
    line-height: 60px;
    font-weight: 700;
    font-family: var(--tecture-font);
}

.coming-soon-page .timer-box .countdown-timer li span.timeRef {
    position: relative;
    display: block;
    color: #ffffff;
    font-size: 18px;
    line-height: 20px;
    font-weight: 700;
    text-transform: uppercase;
    margin-top: 11px;
    font-family: var(--tecture-font);
}

.coming-soon-page__content .inner .text {
    position: relative;
    display: block;
    padding-bottom: 60px;
}

.coming-soon-page__content .inner .text p {
    color: #ffffff;
    font-size: 20px;
    line-height: 30px;
    font-weight: 400;
}

.coming-soon-page__subscribe-box {
    position: relative;
    display: block;
    max-width: 770px;
    width: 100%;
    margin: 0 auto;
}

.coming-soon-page__subscribe-box form {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
    width: 100%;
}

.coming-soon-page__subscribe-box form input[type="email"] {
    position: relative;
    display: block;
    max-width: 490px;
    width: 100%;
    height: 60px;
    border-radius: 0;
    background: var(--tecture-black);
    border: 1px solid var(--tecture-bdr-color) !important;
    color: var(--tecture-gray);
    font-size: 16px;
    font-weight: 400;
    padding-left: 30px;
    padding-right: 30px;
    transition: all 500ms ease 0s;
    font-family: var(--tecture-font);
    outline: none;
}

.coming-soon-page__btn {
    color: var(--tecture-white) !important;
    border: none;
}

.coming-soon-page__btn:hover {
    color: var(--tecture-black) !important;
}