/*--------------------------------------------------------------
# Video One
--------------------------------------------------------------*/
.video-one {
    position: relative;
    display: block;
}

.video-one__inner {
    position: relative;
    display: block;
    z-index: 10;
}

.video-one__main-content {
    position: relative;
    display: block;
    z-index: 1;
}

.video-one__main-content-inner {
    position: relative;
    display: block;
    min-height: 670px;
    z-index: 1;
}

.video-one__main-content-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    z-index: 1;
}

.video-one__main-content-bg:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(31, 32, 34, 0) 0%, rgba(31, 32, 34, 1) 60%);
    opacity: 0.95;
    z-index: -1;
}


.video-one__thumb-box {
    position: absolute;
    top: 50%;
    right: 150px;
    width: 215px;
    height: 385px;
    overflow: hidden;
    transform: translateY(-50%);
    z-index: 99999999;
}

#video-one__thumb {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 5;
}

@media (max-width: 767px) {
    #video-one__thumb {
        display: none;
    }
}

.video-one__img-holder-box {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 215px;
    height: 115px;
    overflow: hidden;
    z-index: 1;
}

.video-one__img-holder {
    position: relative;
    display: block;
    overflow: hidden;
    transition: all 500ms ease;
    width: 215px;
    height: 115px;
    cursor: pointer;
    z-index: 1;
}

.video-one__img-holder:before {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    content: "";
    background-color: rgba(var(--tecture-black-rgb), 0);
    z-index: 1;
    transition: all 500ms ease;
}

.swiper-slide-active .video-one__img-holder:before,
.swiper-slide-thumb-active .video-one__img-holder:before {
    background-color: rgba(var(--tecture-black-rgb), .50);
}

.video-one__img-holder>img {
    width: 100%;
    transition: all 0.5s ease-in-out 0.6s;
}

.video-one__nav {
    display: none;
}


.video-one__curved-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    height: 145px;
    width: 145px;
    border-radius: 50%;
    transform: translateX(-50%) translateY(-50%);
    z-index: 2;
}

.video-one__curved-circle .curved-circle {
    position: absolute;
    font-size: 16px;
    font-weight: 400;
    color: var(--tecture-white);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    word-spacing: 0.3em;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    font-family: var(--tecture-font);
    font-weight: 700;
    font-style: normal;
    height: 145px !important;
    width: 145px;
}

.video-one__video-link {
    position: absolute;
    top: 56%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    z-index: 2;
}

.video-one__video-icon {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
    font-size: 20px;
    color: var(--tecture-white);
    background-color: var(--tecture-base);
    border-radius: 50%;
    transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -webkit-transition: all 0.5s ease;
    -ms-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
}

.video-one__video-icon:hover {
    color: var(--tecture-white);
    background-color: var(--tecture-black);
}



























/*--------------------------------------------------------------
# End
--------------------------------------------------------------*/