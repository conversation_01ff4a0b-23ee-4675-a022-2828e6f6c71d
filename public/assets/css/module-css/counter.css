/*--------------------------------------------------------------
# Counter One
--------------------------------------------------------------*/
.counter-one {
    position: relative;
    display: block;
    overflow: hidden;
    background-color: rgb(31, 32, 34, .4);
    padding: 150px 0 81px;
    z-index: 1;
}

.counter-one__shape {
    position: absolute;
    top: 0;
    right: -100px;
    opacity: .1;
}

.counter-one__shape-1 {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    z-index: -1;
}

.counter-one__inner {
    position: relative;
    display: block;
}

.counter-one__title-box {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    text-align: center;
    margin-bottom: 53px;
}

.counter-one__title-box .section-title {
    display: inline-flex;
    align-items: center;
    margin-bottom: 23px;
}

.counter-one__title-border {
    position: absolute;
    bottom: -18px;
    left: 0;
    right: 0;
    height: 1px;
    background-color: var(--tecture-bdr-color);
}

.counter-one__list {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    padding-bottom: 33px;
}

.counter-one__list li {
    position: relative;
    display: block;
    margin-bottom: 27px;
}

.counter-one__single {
    position: relative;
    display: block;
}

.counter-one__counter-box {
    position: relative;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 12px;
}

.counter-one__counter-box h3 {
    font-size: 72px;
    color: var(--tecture-white);
    font-weight: 700;
    line-height: 72px !important;
    font-family: var(--tecture-font) !important;
    font-style: normal;
}

.counter-one__counter-letter {
    font-size: 72px;
    color: var(--tecture-white);
    font-weight: 700;
    line-height: 72px;
    font-family: var(--tecture-font);
    font-style: normal;
}

.counter-one__counter-plus {
    font-size: 72px;
    color: var(--tecture-white);
    font-weight: 700;
    line-height: 72px;
    font-family: var(--tecture-font);
    font-style: normal;
}

.counter-one__counter-text {
    font-size: 26px;
    color: var(--tecture-white);
    line-height: 36px;
}



























/*--------------------------------------------------------------
# End
--------------------------------------------------------------*/