@font-face {
  font-family: 'icomoon';
  src: url('../fonts/icomoon.eot?orkqwr');
  src: url('../fonts/icomoon.eot?orkqwr#iefix') format('embedded-opentype'),
    url('../fonts/icomoon.ttf?orkqwr') format('truetype'),
    url('../fonts/icomoon.woff?orkqwr') format('woff'),
    url('../fonts/icomoon.svg?orkqwr#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"],
[class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}








.icon-plus:before {
  content: "\e900";
}

.icon-minus:before {
  content: "\e901";
}

.icon-down-arrow:before {
  content: "\e902";
}

.icon-search-interface-symbol:before {
  content: "\e903";
}

.icon-phone-conversation:before {
  content: "\e904";
}

.icon-social-media-marketing:before {
  content: "\e905";
}

.icon-email-marketing:before {
  content: "\e906";
}

.icon-campaign:before {
  content: "\e907";
}

.icon-left-arrow:before {
  content: "\e908";
}

.icon-right-arrow:before {
  content: "\e909";
}

.icon-up-right-arrow:before {
  content: "\e90a";
}

.icon-engineer:before {
  content: "\e90b";
}

.icon-workstations:before {
  content: "\e90c";
}

.icon-cyber-security:before {
  content: "\e90d";
}

.icon-next:before {
  content: "\e90e";
}

.icon-up-right-arrow-1:before {
  content: "\e90f";
}

.icon-pointed-star:before {
  content: "\e910";
}

.icon-paper-plane:before {
  content: "\e911";
}

.icon-globe:before {
  content: "\e912";
}

.icon-text:before {
  content: "\e913";
}

.icon-staircase:before {
  content: "\e914";
}

.icon-plant:before {
  content: "\e915";
}

.icon-vacuum-cleaner:before {
  content: "\e916";
}

.icon-quotes:before {
  content: "\e917";
}

.icon-user:before {
  content: "\e918";
}

.icon-envelope:before {
  content: "\e919";
}

.icon-edit:before {
  content: "\e91a";
}

.icon-info:before {
  content: "\e91b";
}

.icon-customer-service:before {
  content: "\e91c";
}

.icon-passport:before {
  content: "\e91d";
}

.icon-right:before {
  content: "\e91e";
}

.icon-telephone:before {
  content: "\e91f";
}

.icon-download:before {
  content: "\e920";
}

.icon-down-arrow-1:before {
  content: "\e921";
}

.icon-conversation:before {
  content: "\e922";
}

.icon-share:before {
  content: "\e923";
}

.icon-left:before {
  content: "\e924";
}

.icon-calendar:before {
  content: "\e925";
}

.icon-pin:before {
  content: "\e926";
}

.icon-trading:before {
  content: "\e927";
}

.icon-envelope-1:before {
  content: "\e928";
}

.icon-location:before {
  content: "\e929";
}

.icon-mobile-phone:before {
  content: "\e92a";
}

.icon-envelope-2:before {
  content: "\e92b";
}